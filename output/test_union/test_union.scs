fof(goal, plain, p, file('test_union.p', goal)).
fof(goal_negated, plain, ~(p), inference(negate_conjecture, [status(cth)], [goal])).
cnf(1, plain, ~p, inference(cnf, [status(esa)], [goal_negated])).
cnf(l0, plain, ~p, inference(instantiation, [status(thm)], [1])).
fof(axiom1, plain, p, file('test_union.p', axiom1)).
cnf(0, plain, p, inference(cnf, [status(esa)], [axiom1])).
cnf(l1, plain, p, inference(instantiation, [status(thm)], [0])).
cnf(goal_l0_0, plain, p, inference(ground_refutation, [status(thm)], [l0, l1])).
