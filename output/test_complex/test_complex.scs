fof(goal, plain, q, file('test_complex.p', goal)).
fof(goal_negated, plain, ~(q), inference(negate_conjecture, [status(cth)], [goal])).
cnf(2, plain, ~q, inference(cnf, [status(esa)], [goal_negated])).
cnf(l0, plain, ~q, inference(instantiation, [status(thm)], [2])).
fof(axiom1, plain, p|q, file('test_complex.p', axiom1)).
cnf(0, plain, p | q, inference(cnf, [status(esa)], [axiom1])).
cnf(l1, plain, p | q, inference(instantiation, [status(thm)], [0])).
fof(axiom2, plain, ~p, file('test_complex.p', axiom2)).
cnf(1, plain, ~p, inference(cnf, [status(esa)], [axiom2])).
cnf(l2, plain, ~p, inference(instantiation, [status(thm)], [1])).
cnf(goal_l0_0, plain, q, inference(ground_refutation, [status(thm)], [l0, l1, l2])).
cnf(goal_l1_0, plain, ~p, inference(ground_refutation, [status(thm)], [l0, l1, l2])).
