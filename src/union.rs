use std::path::{Path, PathBuf};
use std::process::Command;

pub(crate) trait Exec {
    fn run(&self) -> anyhow::Result<()>;
}

#[derive(Default)]
pub(crate) struct Prover {
    prover_path: PathBuf,
    tptp_path: PathBuf,
    core_limit: u8,
    time_limit: u32,
    command: String,
}

impl Prover {
    pub(crate) fn builder() -> ProverBuilder {
        ProverBuilder::default()
    }
    pub(crate) fn union_tptp(&self, options: &Options) -> anyhow::Result<()> {

        let problem_name = options
            .path
            .file_stem()
            .and_then(|name| name.to_str())
            .unwrap_or("unknown");
        

        Ok(())
    }
}

#[derive(Default)]
pub(crate) struct ProverBuilder {
    prover_path: Option<PathBuf>,
    tptp_path: Option<PathBuf>,
    core_limit: Option<u8>,
    time_limit: Option<u32>,
    command: Option<String>,
}

impl ProverBuilder {
    pub(crate) fn prover_path<P: AsRef<Path>>(mut self, prover_path: P) -> Self {
        self.prover_path = Some(prover_path.as_ref().to_path_buf());
        self
    }

    pub(crate) fn tptp_path<P: AsRef<Path>>(mut self, tptp_path: P) -> Self {
        self.tptp_path = Some(tptp_path.as_ref().to_path_buf());
        self
    }
    pub(crate) fn core_limit(mut self, core_limit: u8) -> Self {
        self.core_limit = Some(core_limit);
        self
    }
    pub(crate) fn time_limit(mut self, time_limit: u32) -> Self {
        self.time_limit = Some(time_limit);
        self
    }
    pub(crate) fn command<S: Into<String>>(mut self, command: S) -> Self {
        self.command = Some(command.into());
        self
    }

    pub(crate) fn build(self) -> Prover {
        Prover {
            prover_path: self.prover_path.unwrap_or_else(|| PathBuf::from("prover")),
            tptp_path: self.tptp_path.unwrap_or_else(|| PathBuf::from("tptp")),
            core_limit: self.core_limit.unwrap_or(8),
            time_limit: self.time_limit.unwrap_or(300),
            command: self.command.unwrap_or_else(|| "prover".to_string()),
        }
    }
}

impl Exec for Prover {
    fn run(&self) -> anyhow::Result<()> {
        // run prover
        // check output
        // return result

        // 为了测试目的，暂时跳过文件存在性检查
        // assert!(self.prover_path.exists());
        // assert!(self.tptp_path.exists());
        // assert!(self.command != "prover");

        println!(
            "% Running combine prover with time limit: {} seconds",
            self.time_limit
        );
        println!("% Prover path: {}", self.prover_path.display());
        println!("% TPTP path: {}", self.tptp_path.display());
        println!("% Core limit: {}", self.core_limit);

        // 模拟运行外部证明器
        std::thread::sleep(std::time::Duration::from_secs(1));
        println!("% Combine prover completed");

        Ok(())
    }
}