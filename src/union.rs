use crate::options::Options;
use std::path::{Path, PathBuf};
use std::process::Command;

pub(crate) trait Exec {
    fn run(&self) -> anyhow::Result<()>;
}

#[derive(Default)]
pub(crate) struct Prover {
    prover_path: PathBuf,
    tptp_path: PathBuf,
    core_limit: u8,
    time_limit: u32,
    command: String,
}

impl Prover {
    pub(crate) fn builder() -> ProverBuilder {
        ProverBuilder::default()
    }
    pub(crate) fn union_tptp(&self, options: &Options) -> anyhow::Result<()> {
        use std::fs;
        use std::io::{BufRead, BufReader, Write};
        use std::path::Path;

        let problem_name = options
            .path
            .file_stem()
            .and_then(|name| name.to_str())
            .unwrap_or("unknown");

        // 第一步：复制原始问题文件并重命名为 scs_问题名.p
        let original_path = &options.path;
        let original_dir = original_path.parent().unwrap_or(Path::new("."));
        let new_filename = format!("scs_{}.p", problem_name);
        let new_file_path = original_dir.join(&new_filename);

        // 复制原始文件
        fs::copy(original_path, &new_file_path)?;

        // 第二步：从 output 目录读取 .scs 文件并处理 goal 和 contradiction 子句
        let output_dir = Path::new("output");
        let problem_dir = output_dir.join(problem_name);
        let scs_file_path = problem_dir.join(format!("{}.scs", problem_name));

        if scs_file_path.exists() {
            let scs_file = fs::File::open(&scs_file_path)?;
            let reader = BufReader::new(scs_file);

            let mut goal_clauses = Vec::new();

            // 读取并处理 .scs 文件中的每一行
            for line in reader.lines() {
                let line = line?;
                let trimmed = line.trim();

                // 检查是否是带有 goal 或 contradiction 的 cnf 子句
                if trimmed.starts_with("cnf(goal_") || trimmed.starts_with("cnf(contradiction,") {
                    // 查找 ", inference(" 的位置
                    if let Some(inference_pos) = trimmed.find(", inference(") {
                        // 提取 inference 之前的部分
                        let clause_without_inference = &trimmed[..inference_pos];
                        // 添加结束的括号和点号
                        goal_clauses.push(format!("{}).", clause_without_inference));
                    } else {
                        // 如果没有找到 inference 部分，直接添加整行
                        goal_clauses.push(trimmed.to_string());
                    }
                }
            }

            // 将处理后的子句追加到新文件中
            if !goal_clauses.is_empty() {
                let mut file = fs::OpenOptions::new().append(true).open(&new_file_path)?;

                writeln!(file, "")?; // 添加空行分隔
                writeln!(file, "% Goal clauses from proof")?;
                for clause in goal_clauses {
                    writeln!(file, "{}", clause)?;
                }
            }
        }

        // 获取新文件的绝对路径并赋值给 tptp_path
        let tptp_path = new_file_path.canonicalize()?;
        self.tptp_path = tptp_path.clone();
        //println!("Generated file path: {}", tptp_path.display());
        Ok(())
    }
}

#[derive(Default)]
pub(crate) struct ProverBuilder {
    prover_path: Option<PathBuf>,
    tptp_path: Option<PathBuf>,
    core_limit: Option<u8>,
    time_limit: Option<u32>,
    command: Option<String>,
}

impl ProverBuilder {
    pub(crate) fn prover_path<P: AsRef<Path>>(mut self, prover_path: P) -> Self {
        self.prover_path = Some(prover_path.as_ref().to_path_buf());
        self
    }

    pub(crate) fn tptp_path<P: AsRef<Path>>(mut self, tptp_path: P) -> Self {
        self.tptp_path = Some(tptp_path.as_ref().to_path_buf());
        self
    }
    pub(crate) fn core_limit(mut self, core_limit: u8) -> Self {
        self.core_limit = Some(core_limit);
        self
    }
    pub(crate) fn time_limit(mut self, time_limit: u32) -> Self {
        self.time_limit = Some(time_limit);
        self
    }
    pub(crate) fn command<S: Into<String>>(mut self, command: S) -> Self {
        self.command = Some(command.into());
        self
    }

    pub(crate) fn build(self) -> Prover {
        Prover {
            prover_path: self.prover_path.unwrap_or_else(|| PathBuf::from("prover")),
            tptp_path: self.tptp_path.unwrap_or_else(|| PathBuf::from("tptp")),
            core_limit: self.core_limit.unwrap_or(8),
            time_limit: self.time_limit.unwrap_or(300),
            command: self.command.unwrap_or_else(|| "prover".to_string()),
        }
    }
}

impl Exec for Prover {
    fn run(&self) -> anyhow::Result<()> {
        // run prover
        // check output
        // return result

        // 为了测试目的，暂时跳过文件存在性检查
        // assert!(self.prover_path.exists());
        // assert!(self.tptp_path.exists());
        // assert!(self.command != "prover");

        println!(
            "% Running combine prover with time limit: {} seconds",
            self.time_limit
        );
        println!("% Prover path: {}", self.prover_path.display());
        println!("% TPTP path: {}", self.tptp_path.display());
        println!("% Core limit: {}", self.core_limit);

        // 模拟运行外部证明器
        std::thread::sleep(std::time::Duration::from_secs(1));
        println!("% Combine prover completed");

        Ok(())
    }
}
